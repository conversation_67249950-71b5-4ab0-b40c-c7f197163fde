import tkinter as tk
from tkinter import ttk, messagebox
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor

class APIConfigPanel(ttk.LabelFrame):
    def __init__(self, parent, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.create_api_config_frame()

    def create_api_config_frame(self):
        """创建API配置框架（在设置标签页中）"""
        api_frame = ttk.LabelFrame(self, text="API配置", padding="5")
        api_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # API基础URL
        ttk.Label(api_frame, text="API基础URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.api_base_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_base_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # API密钥
        ttk.Label(api_frame, text="API密钥:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_key_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 模型名称
        ttk.Label(api_frame, text="模型名称:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.model_name_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.model_name_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 温度设置
        ttk.Label(api_frame, text="温度:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.temperature_var = tk.DoubleVar(value=0.7)
        temp_frame = ttk.Frame(api_frame)
        temp_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Scale(temp_frame, from_=0.0, to=1.0, variable=self.temperature_var,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT)
        ttk.Label(temp_frame, textvariable=self.temperature_var).pack(side=tk.LEFT, padx=5)

        # 最大token数
        ttk.Label(api_frame, text="最大Token数:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.max_tokens_var = tk.IntVar(value=2000)
        ttk.Entry(api_frame, textvariable=self.max_tokens_var, width=40).grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 测试连接按钮
        ttk.Button(api_frame, text="测试连接", command=self.test_connection).grid(row=5, column=0, columnspan=2, pady=10)

        api_frame.columnconfigure(1, weight=1)

    def test_connection(self, processor=None):
        try:
            # 如果没有传入processor，则创建一个新的
            if processor is None:
                from config import Config
                config = Config()
                config.API_BASE_URL = self.api_base_var.get()
                config.API_KEY = self.api_key_var.get()
                config.MODEL_NAME = self.model_name_var.get()
                config.TEMPERATURE = self.temperature_var.get()
                config.MAX_TOKENS = self.max_tokens_var.get()
                processor = DocumentToQuizProcessor(config)

            # 测试连接
            if processor.test_api_connection():
                messagebox.showinfo("成功", "API连接测试成功！")
            else:
                messagebox.showerror("错误", "API连接测试失败！")
        except Exception as e:
            messagebox.showerror("错误", f"测试连接时发生错误：{str(e)}")