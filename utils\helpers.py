"""
辅助工具函数
"""

import os
import logging
import argparse
from typing import List, Dict, Any

def setup_logging(log_level: str = "INFO", log_format: str = None):
    """
    设置日志配置

    Args:
        log_level: 日志级别
        log_format: 日志格式
    """
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('document_to_quiz.log', encoding='utf-8')
        ]
    )

def validate_input_directory(directory: str, recursive: bool = True) -> bool:
    """
    验证输入目录

    Args:
        directory: 目录路径
        recursive: 是否递归检查子文件夹，默认为True

    Returns:
        是否有效
    """
    if not os.path.exists(directory):
        return False

    if not os.path.isdir(directory):
        return False

    # 检查是否有支持的文档文件
    supported_extensions = ['.doc', '.docx', '.pdf']

    if recursive:
        # 递归检查所有子目录
        for root, dirs, files in os.walk(directory):
            for filename in files:
                if any(filename.lower().endswith(ext) for ext in supported_extensions):
                    return True
    else:
        # 只检查当前目录
        for filename in os.listdir(directory):
            if any(filename.lower().endswith(ext) for ext in supported_extensions):
                return True

    return False

def scan_directory_structure(directory: str, recursive: bool = True, supported_extensions: List[str] = None) -> Dict[str, Any]:
    """
    扫描目录结构，统计文档文件信息

    Args:
        directory: 目录路径
        recursive: 是否递归扫描子文件夹
        supported_extensions: 支持的文件扩展名列表，如果为None则使用默认值

    Returns:
        目录结构信息字典
    """
    if not os.path.exists(directory) or not os.path.isdir(directory):
        return {'error': '目录不存在或不是有效目录'}

    if supported_extensions is None:
        supported_extensions = ['.docx', '.pdf', '.md', '.txt']
    structure = {
        'total_files': 0,
        'supported_files': 0,
        'file_types': {},
        'directories': [],
        'files_by_directory': {}
    }

    try:
        if recursive:
            # 递归扫描所有子目录
            for root, dirs, files in os.walk(directory):
                relative_root = os.path.relpath(root, directory)
                if relative_root == '.':
                    relative_root = ''

                structure['directories'].append(relative_root)
                structure['files_by_directory'][relative_root] = []

                for filename in files:
                    structure['total_files'] += 1
                    file_ext = os.path.splitext(filename)[1].lower()

                    if file_ext in supported_extensions:
                        structure['supported_files'] += 1
                        structure['file_types'][file_ext] = structure['file_types'].get(file_ext, 0) + 1
                        structure['files_by_directory'][relative_root].append(filename)
        else:
            # 只扫描当前目录
            structure['directories'].append('')
            structure['files_by_directory'][''] = []

            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    structure['total_files'] += 1
                    file_ext = os.path.splitext(filename)[1].lower()

                    if file_ext in supported_extensions:
                        structure['supported_files'] += 1
                        structure['file_types'][file_ext] = structure['file_types'].get(file_ext, 0) + 1
                        structure['files_by_directory'][''].append(filename)

    except Exception as e:
        structure['error'] = f'扫描目录时发生错误: {str(e)}'

    return structure

def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器

    Returns:
        参数解析器
    """
    parser = argparse.ArgumentParser(
        description="文档到题库工具 - 将DOC/DOCX/PDF文档转换为题库CSV文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --input_dir ./documents --output_dir ./output --api_key YOUR_API_KEY
  python main.py --input_dir ./docs --api_base https://api.example.com/v1 --model gpt-4
  python main.py --input_dir ./docs --questions_per_chunk 5 --max_chunk_size 3000
        """
    )

    # 必需参数
    parser.add_argument(
        '--input_dir',
        required=True,
        help='输入文档目录路径'
    )

    # API配置
    parser.add_argument(
        '--api_key',
        help='OpenAI兼容API的密钥'
    )

    parser.add_argument(
        '--api_base',
        help='API基础URL (默认: https://api.openai.com/v1)'
    )

    parser.add_argument(
        '--model',
        help='使用的模型名称 (默认: gpt-3.5-turbo)'
    )

    # 输出配置
    parser.add_argument(
        '--output_dir',
        default='output',
        help='输出目录路径 (默认: output)'
    )

    # 处理参数
    parser.add_argument(
        '--questions_per_chunk',
        type=int,
        help='每个文本块生成的题目数量 (默认: 3)'
    )

    parser.add_argument(
        '--max_chunk_size',
        type=int,
        help='每个文本块的最大字符数 (默认: 4000)'
    )

    # 其他选项
    parser.add_argument(
        '--resume',
        action='store_true',
        help='从上次中断的地方继续处理'
    )

    parser.add_argument(
        '--test_connection',
        action='store_true',
        help='仅测试API连接，不处理文档'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='显示详细日志'
    )

    parser.add_argument(
        '--cleanup_errors',
        type=int,
        metavar='DAYS',
        help='清理指定天数前的错误文件'
    )

    parser.add_argument(
        '--no-recursive',
        action='store_true',
        help='不递归处理子文件夹，只处理指定目录下的文件'
    )

    return parser

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    文档到题库转换工具                          ║
║                  Document to Quiz Tool                      ║
║                                                              ║
║  支持DOC/DOCX文档，调用OpenAI兼容API生成题库，输出CSV格式      ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_progress_bar(current: int, total: int, prefix: str = "", suffix: str = "", length: int = 50):
    """
    打印进度条

    Args:
        current: 当前进度
        total: 总数
        prefix: 前缀文本
        suffix: 后缀文本
        length: 进度条长度
    """
    if total == 0:
        return

    percent = (current / total) * 100
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)

    print(f'\r{prefix} |{bar}| {current}/{total} ({percent:.1f}%) {suffix}', end='', flush=True)

    if current == total:
        print()  # 换行

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小

    Args:
        size_bytes: 字节数

    Returns:
        格式化的大小字符串
    """
    if size_bytes == 0:
        return "0B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0

    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"

def validate_api_config(config) -> List[str]:
    """
    验证API配置

    Args:
        config: 配置对象

    Returns:
        错误信息列表
    """
    errors = []

    if not config.API_KEY:
        errors.append("API_KEY不能为空")

    if not config.API_BASE_URL:
        errors.append("API_BASE_URL不能为空")

    if not config.MODEL_NAME:
        errors.append("MODEL_NAME不能为空")

    return errors

def estimate_processing_time(total_chunks: int, questions_per_chunk: int = 3) -> str:
    """
    估算处理时间

    Args:
        total_chunks: 总文本块数
        questions_per_chunk: 每块题目数

    Returns:
        估算时间字符串
    """
    # 假设每个API调用平均需要3秒
    estimated_seconds = total_chunks * 3

    if estimated_seconds < 60:
        return f"约 {estimated_seconds} 秒"
    elif estimated_seconds < 3600:
        minutes = estimated_seconds // 60
        return f"约 {minutes} 分钟"
    else:
        hours = estimated_seconds // 3600
        minutes = (estimated_seconds % 3600) // 60
        return f"约 {hours} 小时 {minutes} 分钟"

def summarize_results(questions: List[Dict[str, Any]], failed_chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    汇总处理结果

    Args:
        questions: 生成的题目列表
        failed_chunks: 失败的文本块列表

    Returns:
        结果摘要
    """
    # 统计文件
    processed_files = set()
    failed_files = set()

    for question in questions:
        source_file = question.get('source_file', '')
        if source_file:
            processed_files.add(source_file)

    for chunk in failed_chunks:
        source_file = chunk.get('filename', '')
        if source_file:
            failed_files.add(source_file)

    # 统计题目类型
    question_types = {}
    for question in questions:
        q_type = question.get('type', '未知')
        question_types[q_type] = question_types.get(q_type, 0) + 1

    return {
        'total_questions': len(questions),
        'total_failed_chunks': len(failed_chunks),
        'processed_files_count': len(processed_files),
        'failed_files_count': len(failed_files),
        'processed_files': list(processed_files),
        'failed_files': list(failed_files),
        'question_types': question_types,
        'success_rate': len(questions) / (len(questions) + len(failed_chunks)) * 100 if (len(questions) + len(failed_chunks)) > 0 else 0
    }

def play_sound_alert(sound_type: str = "error") -> None:
    """
    播放声音提示

    Args:
        sound_type: 声音类型，可选值: "error", "warning", "info"
    """
    try:
        # 尝试导入playsound库
        from playsound import playsound

        # 根据声音类型选择不同的系统声音
        if sound_type == "error":
            # 播放系统错误声音
            import winsound
            winsound.MessageBeep(winsound.MB_ICONHAND)
        elif sound_type == "warning":
            # 播放系统警告声音
            import winsound
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        elif sound_type == "info":
            # 播放系统信息声音
            import winsound
            winsound.MessageBeep(winsound.MB_ICONASTERISK)
        else:
            # 默认播放系统默认声音
            import winsound
            winsound.MessageBeep(winsound.MB_OK)

    except ImportError:
        # 如果playsound库不可用，使用系统内置的声音
        try:
            import winsound
            if sound_type == "error":
                winsound.MessageBeep(winsound.MB_ICONHAND)
            elif sound_type == "warning":
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            elif sound_type == "info":
                winsound.MessageBeep(winsound.MB_ICONASTERISK)
            else:
                winsound.MessageBeep(winsound.MB_OK)
        except ImportError:
            # 如果连winsound都不可用，则静默失败
            pass
    except Exception as e:
        # 播放声音失败时静默处理，不影响主程序
        logging.getLogger(__name__).debug(f"播放声音失败: {str(e)}")
