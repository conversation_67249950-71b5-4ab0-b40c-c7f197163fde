import tkinter as tk
from tkinter import ttk, filedialog

class OutputConfigPanel(ttk.LabelFrame):
    def __init__(self, parent, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.create_output_config_frame()

    def create_output_config_frame(self):
        """创建输出配置框架（在主界面中）"""
        config_frame = ttk.LabelFrame(self, text="输出配置", padding="5")
        config_frame.pack(fill=tk.X, pady=5)

        # CSV文件位置
        ttk.Label(config_frame, text="CSV文件位置:").grid(row=0, column=0, sticky=tk.W)
        self.csv_path_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_path_var).grid(row=0, column=1, sticky=(tk.W, tk.E))
        ttk.Button(config_frame, text="浏览", command=self.select_csv_path).grid(row=0, column=2, padx=5)

        # CSV文件名
        ttk.Label(config_frame, text="CSV文件名:").grid(row=1, column=0, sticky=tk.W)
        self.csv_name_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_name_var).grid(row=1, column=1, sticky=(tk.W, tk.E))

        # CSV模板
        ttk.Label(config_frame, text="CSV模板:").grid(row=2, column=0, sticky=tk.W)
        self.csv_template_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_template_var).grid(row=2, column=1, sticky=(tk.W, tk.E))

    def select_csv_path(self):
        csv_path = filedialog.askdirectory()
        if csv_path:
            self.csv_path_var.set(csv_path)