#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本分割器功能
验证删除 MIN_CHUNK_SIZE、MAX_MERGE_SIZE 和 CHUNK_OVERLAP 后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from document_processor.text_splitter import TextSplitter

def test_text_splitter():
    """测试文本分割器基本功能"""
    print("=== 测试文本分割器功能 ===")
    
    # 创建文本分割器实例
    splitter = TextSplitter(
        max_chunk_size=2000,
        disable_splitting=False,
        enable_chunk_merging=True,
        use_new_splitting_logic=True
    )
    
    # 测试文档
    test_doc = {
        'filename': 'test_document.txt',
        'content': '''这是一个测试文档的内容。
        
第一章 概述
这是第一章的内容，包含了一些基本的介绍信息。这里有很多文字内容，用来测试文本分割功能是否正常工作。

第二章 详细说明
这是第二章的内容，包含了更详细的说明。这一章的内容比较长，可能会被分割成多个文本块。

第三章 总结
这是第三章的内容，是对前面内容的总结。这一章相对较短。

结论
这是文档的结论部分。''',
        'structure': []
    }
    
    print(f"原始文档字符数: {len(test_doc['content'])}")
    
    # 测试分割功能
    chunks = splitter.split_document(test_doc)
    
    print(f"分割后的文本块数量: {len(chunks)}")
    
    for i, chunk in enumerate(chunks):
        print(f"\n--- 文本块 {i+1} ---")
        print(f"字符数: {chunk.get('char_count', '未知')}")
        print(f"Token数: {chunk.get('token_count', '未知')}")
        print(f"内容预览: {chunk['content'][:100]}...")
    
    # 测试合并功能
    if len(chunks) > 1:
        print("\n=== 测试合并功能 ===")
        merged_chunk, next_index = splitter.try_merge_with_next_chunks(chunks, 0)
        if merged_chunk:
            print(f"合并成功，合并后字符数: {merged_chunk.get('char_count', '未知')}")
            print(f"下一个处理索引: {next_index}")
        else:
            print("合并失败")
    
    print("\n=== 测试完成 ===")
    return True

def test_different_configurations():
    """测试不同配置下的文本分割器"""
    print("\n=== 测试不同配置 ===")
    
    configs = [
        {
            'name': '标准配置',
            'max_chunk_size': 1000,
            'disable_splitting': False,
            'enable_chunk_merging': True,
            'use_new_splitting_logic': False
        },
        {
            'name': '新逻辑配置',
            'max_chunk_size': 1000,
            'disable_splitting': False,
            'enable_chunk_merging': True,
            'use_new_splitting_logic': True
        },
        {
            'name': '禁用分割',
            'max_chunk_size': 1000,
            'disable_splitting': True,
            'enable_chunk_merging': True,
            'use_new_splitting_logic': False
        }
    ]
    
    test_content = "这是一个测试内容。" * 200  # 创建较长的测试内容
    test_doc = {
        'filename': 'test.txt',
        'content': test_content,
        'structure': []
    }
    
    for config in configs:
        print(f"\n--- {config['name']} ---")
        splitter = TextSplitter(**{k: v for k, v in config.items() if k != 'name'})
        chunks = splitter.split_document(test_doc)
        print(f"分块数量: {len(chunks)}")
        if chunks:
            print(f"第一块字符数: {chunks[0].get('char_count', len(chunks[0]['content']))}")

if __name__ == "__main__":
    try:
        test_text_splitter()
        test_different_configurations()
        print("\n[成功] 所有测试通过！")
    except Exception as e:
        print(f"\n[失败] 测试失败: {e}")
        import traceback
        traceback.print_exc()
