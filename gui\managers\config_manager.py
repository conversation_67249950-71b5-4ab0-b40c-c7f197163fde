import json
import tkinter as tk
from typing import Dict, Any

class ConfigManager:
    def __init__(self):
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 设置LLM配置的默认值
                if 'api_base' not in config:
                    config['api_base'] = ''
                if 'api_key' not in config:
                    config['api_key'] = ''
                if 'model_name' not in config:
                    config['model_name'] = 'gpt-3.5-turbo'
                if 'temperature' not in config:
                    config['temperature'] = 0.7
                if 'max_tokens' not in config:
                    config['max_tokens'] = 2000
                # 设置文本处理配置的默认值
                if 'max_chunk_size' not in config:
                    config['max_chunk_size'] = 2000
                if 'question_base_chars' not in config:
                    config['question_base_chars'] = 2000
                # 设置题型数量配置的默认值
                if 'single_choice_count' not in config:
                    config['single_choice_count'] = 4
                if 'multiple_choice_count' not in config:
                    config['multiple_choice_count'] = 2
                if 'fill_blank_count' not in config:
                    config['fill_blank_count'] = 2
                if 'short_answer_count' not in config:
                    config['short_answer_count'] = 1
                if 'true_false_count' not in config:
                    config['true_false_count'] = 2
                if 'sorting_count' not in config:
                    config['sorting_count'] = 0
                # 设置文档分割配置的默认值
                if 'disable_document_splitting' not in config:
                    config['disable_document_splitting'] = False
                # 设置分块合并配置的默认值
                if 'enable_chunk_merging' not in config:
                    config['enable_chunk_merging'] = True  # 默认启用
                if 'use_new_splitting_logic' not in config:
                    config['use_new_splitting_logic'] = True
                # 设置文件类型配置的默认值
                if 'enable_pdf' not in config:
                    config['enable_pdf'] = True
                if 'enable_docx' not in config:
                    config['enable_docx'] = True
                if 'enable_md' not in config:
                    config['enable_md'] = True
                if 'enable_txt' not in config:
                    config['enable_txt'] = True
                return config
        except FileNotFoundError:
            return {
                'csv_path': '',
                'csv_name': 'output.csv',
                'csv_template': 'template.csv',
                'api_base': '',
                'api_key': '',
                'model_name': 'gpt-3.5-turbo',
                'temperature': 0.7,
                'max_tokens': 2000,
                'max_chunk_size': 2000,
                'question_base_chars': 2000,
                'single_choice_count': 4,
                'multiple_choice_count': 2,
                'fill_blank_count': 2,
                'short_answer_count': 1,
                'true_false_count': 2,
                'sorting_count': 0,
                'disable_document_splitting': False,
                'enable_chunk_merging': True,  # 默认启用
                'use_new_splitting_logic': True,
                'enable_pdf': True,
                'enable_docx': True,
                'enable_md': True,
                'enable_txt': True
            }

    def save_config(self, config: Dict[str, Any]) -> None:
        """保存配置到文件"""
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

    def load_config_to_gui(self, gui_vars: Dict[str, tk.Variable]) -> None:
        """将配置加载到GUI控件中"""
        # 加载CSV配置
        gui_vars['csv_path_var'].set(self.config.get('csv_path', ''))
        gui_vars['csv_name_var'].set(self.config.get('csv_name', 'output.csv'))
        gui_vars['csv_template_var'].set(self.config.get('csv_template', 'template.csv'))

        # 加载LLM配置
        gui_vars['api_base_var'].set(self.config.get('api_base', ''))
        gui_vars['api_key_var'].set(self.config.get('api_key', ''))
        gui_vars['model_name_var'].set(self.config.get('model_name', 'gpt-3.5-turbo'))
        gui_vars['temperature_var'].set(self.config.get('temperature', 0.7))
        gui_vars['max_tokens_var'].set(self.config.get('max_tokens', 2000))

        # 加载文本处理配置
        gui_vars['max_chunk_size_var'].set(self.config.get('max_chunk_size', 2000))
        gui_vars['question_base_chars_var'].set(self.config.get('question_base_chars', 2000))

        # 加载题型数量配置
        gui_vars['single_choice_var'].set(self.config.get('single_choice_count', 4))
        gui_vars['multiple_choice_var'].set(self.config.get('multiple_choice_count', 2))
        gui_vars['fill_blank_var'].set(self.config.get('fill_blank_count', 2))
        gui_vars['short_answer_var'].set(self.config.get('short_answer_count', 1))
        gui_vars['true_false_var'].set(self.config.get('true_false_count', 2))
        gui_vars['sorting_var'].set(self.config.get('sorting_count', 0))

        # 加载文件类型选择配置
        gui_vars['enable_pdf_var'].set(self.config.get('enable_pdf', True))
        gui_vars['enable_docx_var'].set(self.config.get('enable_docx', True))
        gui_vars['enable_md_var'].set(self.config.get('enable_md', True))
        gui_vars['enable_txt_var'].set(self.config.get('enable_txt', True))

        # 加载文档分割配置
        gui_vars['disable_splitting_var'].set(self.config.get('disable_document_splitting', False))
        gui_vars['use_new_splitting_var'].set(self.config.get('use_new_splitting_logic', True))

    def save_config_and_notify(self, gui_vars: Dict[str, tk.Variable], status_var: tk.StringVar) -> None:
        """保存配置并通知状态"""
        try:
            config = {
                'csv_path': gui_vars['csv_path_var'].get(),
                'csv_name': gui_vars['csv_name_var'].get(),
                'csv_template': gui_vars['csv_template_var'].get(),
                'api_base': gui_vars['api_base_var'].get(),
                'api_key': gui_vars['api_key_var'].get(),
                'model_name': gui_vars['model_name_var'].get(),
                'temperature': gui_vars['temperature_var'].get(),
                'max_tokens': gui_vars['max_tokens_var'].get(),
                'max_chunk_size': gui_vars['max_chunk_size_var'].get(),
                'question_base_chars': gui_vars['question_base_chars_var'].get(),
                'single_choice_count': gui_vars['single_choice_var'].get(),
                'multiple_choice_count': gui_vars['multiple_choice_var'].get(),
                'fill_blank_count': gui_vars['fill_blank_var'].get(),
                'short_answer_count': gui_vars['short_answer_var'].get(),
                'true_false_count': gui_vars['true_false_var'].get(),
                'sorting_count': gui_vars['sorting_var'].get(),
                'enable_pdf': gui_vars['enable_pdf_var'].get(),
                'enable_docx': gui_vars['enable_docx_var'].get(),
                'enable_md': gui_vars['enable_md_var'].get(),
                'enable_txt': gui_vars['enable_txt_var'].get(),
                'disable_document_splitting': gui_vars['disable_splitting_var'].get(),
                'enable_chunk_merging': gui_vars['enable_chunk_merging_var'].get(),
                'use_new_splitting_logic': gui_vars['use_new_splitting_var'].get()
            }
            self.save_config(config)
            status_var.set("设置已保存")
        except Exception as e:
            status_var.set(f"保存设置失败: {str(e)}")