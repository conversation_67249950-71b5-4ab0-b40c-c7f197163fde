import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from utils.helpers import scan_directory_structure

class FileSelectionPanel(ttk.Frame):
    def __init__(self, parent, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.create_file_selection_frame()

    def create_file_selection_frame(self):
        file_frame = ttk.LabelFrame(self, text="文件选择", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        # 文件选择按钮
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=0, padx=5)
        ttk.Button(file_frame, text="选择文件夹", command=self.select_folder).grid(row=0, column=1, padx=5)

        # 递归选项
        self.recursive_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(file_frame, text="递归读取子文件夹", variable=self.recursive_var).grid(row=0, column=2, padx=5)

        # 显示选择的路径
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, textvariable=self.file_path_var).grid(row=1, column=0, columnspan=3, sticky=tk.W)

        # 文件类型选择
        file_types_frame = ttk.LabelFrame(file_frame, text="选择处理的文件类型", padding="3")
        file_types_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # 创建文件类型复选框变量
        self.enable_pdf_var = tk.BooleanVar(value=True)
        self.enable_docx_var = tk.BooleanVar(value=True)
        self.enable_md_var = tk.BooleanVar(value=True)
        self.enable_txt_var = tk.BooleanVar(value=True)

        # 创建复选框
        ttk.Checkbutton(file_types_frame, text="PDF", variable=self.enable_pdf_var).grid(row=0, column=0, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="DOCX", variable=self.enable_docx_var).grid(row=0, column=1, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="MD", variable=self.enable_md_var).grid(row=0, column=2, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="TXT", variable=self.enable_txt_var).grid(row=1, column=0, padx=5, sticky=tk.W)

        # 全选/全不选按钮
        button_frame = ttk.Frame(file_types_frame)
        button_frame.grid(row=1, column=2, padx=5, sticky=tk.W)
        ttk.Button(button_frame, text="全选", command=self.select_all_file_types, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="全不选", command=self.deselect_all_file_types, width=8).pack(side=tk.LEFT, padx=2)

        # 目录结构预览按钮
        ttk.Button(file_frame, text="预览目录结构", command=self.preview_directory).grid(row=3, column=0, columnspan=3, pady=5)

    def select_file(self):
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)

    def select_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.file_path_var.set(folder_path)

    def preview_directory(self):
        """预览目录结构"""
        directory = self.file_path_var.get()
        if not directory:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.isdir(directory):
            messagebox.showwarning("警告", "选择的路径不是文件夹")
            return

        try:
            recursive = self.recursive_var.get()
            # 获取当前启用的文件类型
            enabled_formats = []
            if self.enable_pdf_var.get():
                enabled_formats.append('.pdf')
            if self.enable_docx_var.get():
                enabled_formats.append('.docx')
            if self.enable_md_var.get():
                enabled_formats.append('.md')
            if self.enable_txt_var.get():
                enabled_formats.append('.txt')

            structure_info = scan_directory_structure(directory, recursive, enabled_formats)

            if 'error' in structure_info:
                messagebox.showerror("错误", structure_info['error'])
                return

            # 创建预览窗口
            preview_window = tk.Toplevel(self)
            preview_window.title("目录结构预览")
            preview_window.geometry("600x400")

            # 创建文本框显示结构信息
            text_frame = ttk.Frame(preview_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 构建显示内容
            content = f"目录结构预览 (递归: {recursive})\n"
            content += "=" * 50 + "\n\n"
            content += f"总文件数: {structure_info['total_files']}\n"
            content += f"支持的文档数: {structure_info['supported_files']}\n"
            content += f"文件类型分布: {structure_info['file_types']}\n\n"

            content += "各目录文件分布:\n"
            content += "-" * 30 + "\n"

            for directory_path, files in structure_info['files_by_directory'].items():
                if files:  # 只显示有支持文件的目录
                    display_path = directory_path if directory_path else "根目录"
                    content += f"\n📁 {display_path} ({len(files)} 个文件):\n"
                    for file in files:
                        content += f"  📄 {file}\n"

            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"预览目录结构时发生错误：{str(e)}")

    def select_all_file_types(self):
        """全选文件类型"""
        self.enable_pdf_var.set(True)
        self.enable_docx_var.set(True)
        self.enable_md_var.set(True)
        self.enable_txt_var.set(True)

    def deselect_all_file_types(self):
        """全不选文件类型"""
        self.enable_pdf_var.set(False)
        self.enable_docx_var.set(False)
        self.enable_md_var.set(False)
        self.enable_txt_var.set(False)